{"kind": "collectionType", "collectionName": "video_features", "info": {"singularName": "video-feature", "pluralName": "video-features", "displayName": "video-feature"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "video_id": {"type": "string", "unique": true}, "key_id": {"type": "string"}, "aes_key": {"type": "string"}, "manifest_url": {"type": "string"}, "thumbnail": {"type": "string"}, "description": {"type": "text"}, "grades": {"type": "relation", "relation": "manyToMany", "target": "api::grade.grade", "inversedBy": "video_features"}, "chapters": {"type": "relation", "relation": "manyToMany", "target": "api::chapter.chapter"}, "courses": {"type": "relation", "relation": "manyToMany", "target": "api::course.course"}, "document": {"type": "string"}, "status_video": {"type": "enumeration", "enum": ["processing", "ready"]}}}